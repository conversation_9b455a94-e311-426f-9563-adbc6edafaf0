import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route } from "react-router-dom";
import { HelmetProvider } from "react-helmet-async";
import { CursorProvider } from "./context/CursorContext";
import ErrorBoundary from "./components/ErrorBoundary";
import { useFontLoader } from "./hooks/useFontLoader";
import Index from "./pages/Index";
import Projects from "./pages/Projects";
import Blog from "./components/pages/Blog";
import BlogPost from "./pages/BlogPost";
import Contact from "./pages/Contact";
import NotFound from "./pages/NotFound";
import { useEffect } from "react";

const queryClient = new QueryClient();

const App = () => {
  // Preload critical fonts to prevent FOUT
  const { fontsLoaded, fontLoadingError } = useFontLoader([
    { family: 'Poppins', weights: [300, 400, 500, 600, 700] },
    { family: 'Fustat', weights: [200, 300, 400, 500, 600, 700, 800] },
    { family: 'Cairo', weights: [300, 400, 500, 600, 700] },
    { family: 'Rakkas' },
    { family: 'Limelight' }
  ]);

  // Add fonts-loaded class to body when fonts are ready
  useEffect(() => {
    if (fontsLoaded) {
      document.body.classList.add('fonts-loaded');
    }
  }, [fontsLoaded]);

  // Log font loading errors in development
  useEffect(() => {
    if (fontLoadingError && process.env.NODE_ENV === 'development') {
      console.warn('Font loading error:', fontLoadingError);
    }
  }, [fontLoadingError]);

  return (
    <ErrorBoundary>
      <HelmetProvider>
        <QueryClientProvider client={queryClient}>
          <TooltipProvider>
            <CursorProvider>
              <Toaster />
              <Sonner />
              <BrowserRouter>
                <ErrorBoundary>
                  <Routes>
                    <Route path="/" element={<Index />} />
                    <Route path="/projects" element={<Projects />} />
                    <Route path="/blog" element={<Blog />} />
                    <Route path="/blog/:slug" element={<BlogPost />} />
                    <Route path="/contact" element={<Contact />} />
                    <Route path="*" element={<NotFound />} />
                  </Routes>
                </ErrorBoundary>
              </BrowserRouter>
            </CursorProvider>
          </TooltipProvider>
        </QueryClientProvider>
      </HelmetProvider>
    </ErrorBoundary>
  );
};

export default App;
