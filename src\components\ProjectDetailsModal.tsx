import { motion, AnimatePresence } from 'framer-motion';
import { X, Eye, Globe, Code, Tag, Sparkles, Layers, Palette, Smartphone, MessageSquare, Zap } from 'lucide-react';
import { Button } from './ui/button';
import { InteractiveHoverButton } from './ui/interactive-hover-button';
import { useTranslation } from 'react-i18next';
import { Project } from '../pages/Projects';
import { Badge } from './ui/badge';
import { getFontClass } from '../i18n';
import { useState, useEffect, useRef } from 'react';
import { OptimizedImage } from './ui/optimized-image';

interface ProjectDetailsModalProps {
  project: Project;
  isOpen: boolean;
  onClose: () => void;
  onPreview: () => void;
}

// CSS to hide scrollbars in the modal
const noScrollbarStyle = `
  .project-modal-content {
    -ms-overflow-style: none !important;
    scrollbar-width: none !important;
  }
  
  .project-modal-content::-webkit-scrollbar {
    display: none !important;
  }
`;

export default function ProjectDetailsModal({
  project,
  isOpen,
  onClose,
  onPreview
}: ProjectDetailsModalProps) {
  const { t } = useTranslation();
  const [activeTab, setActiveTab] = useState<'about' | 'overview' | 'features' | 'tech'>('about');
  const modalRef = useRef<HTMLDivElement>(null);
  const closeButtonRef = useRef<HTMLButtonElement>(null);

  // Focus management
  useEffect(() => {
    if (isOpen && closeButtonRef.current) {
      closeButtonRef.current.focus();
    }
  }, [isOpen]);

  // Keyboard navigation
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key === 'Escape') {
        onClose();
      }
    };

    if (isOpen) {
      document.addEventListener('keydown', handleKeyDown);
      // Prevent body scroll when modal is open
      document.body.style.overflow = 'hidden';
    }

    return () => {
      document.removeEventListener('keydown', handleKeyDown);
      document.body.style.overflow = 'unset';
    };
  }, [isOpen, onClose]);

  if (!isOpen) return null;
  
  // Map feature icons based on content
  const getFeatureIcon = (index: number) => {
    const icons = [
      <Sparkles className="h-5 w-5" />,
      <Smartphone className="h-5 w-5" />,
      <Palette className="h-5 w-5" />,
      <MessageSquare className="h-5 w-5" />,
      <Layers className="h-5 w-5" />,
      <Zap className="h-5 w-5" />
    ];
    return icons[index % icons.length];
  };

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      className="fixed inset-0 z-50 flex items-center justify-center bg-black/80 backdrop-blur-sm p-4 md:p-8"
      onClick={onClose}
      role="dialog"
      aria-modal="true"
      aria-labelledby="modal-title"
      aria-describedby="modal-description"
      ref={modalRef}
    >
      {/* Apply scrollbar hiding styles to the modal */}
      <style>{noScrollbarStyle}</style>
      
      <motion.div
        initial={{ scale: 0.9, opacity: 0 }}
        animate={{ scale: 1, opacity: 1 }}
        exit={{ scale: 0.95, opacity: 0 }}
        transition={{ duration: 0.3, type: 'spring', stiffness: 300, damping: 30 }}
        className="bg-dark border border-white/10 rounded-lg w-full max-w-4xl max-h-[90vh] overflow-hidden flex flex-col shadow-2xl shadow-black/50 project-modal-content"
        onClick={(e) => e.stopPropagation()}
      >
        {/* Project Image Banner */}
        <div className="w-full relative bg-[#121212] rounded-t-lg overflow-hidden">
          <div className="absolute top-4 right-4 z-30">
            <Button
              ref={closeButtonRef}
              variant="ghost"
              size="icon"
              className="bg-black/50 backdrop-blur-sm text-white hover:bg-black/70 rounded-full h-8 w-8"
              onClick={onClose}
              aria-label={t('common.close', 'Close modal')}
            >
              <X className="h-4 w-4" />
            </Button>
          </div>
          
          <motion.div 
            className="w-full aspect-video overflow-hidden"
            initial={{ opacity: 0.8 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.5 }}
          >
            <motion.div 
              className="w-full h-full"
              initial={{ scale: 1.05 }}
              animate={{ scale: 1 }}
              transition={{ duration: 1.2, ease: 'easeOut' }}
            >
              <OptimizedImage 
                src={project.image} 
                alt={project.title}
                className="w-full h-auto object-cover z-10"
                priority={true}
                loading="eager"
                sizes="(max-width: 1280px) 100vw, 1280px"
              />
            </motion.div>
            <div className="absolute inset-0 bg-gradient-to-b from-transparent to-dark z-20 pointer-events-none"></div>
          </motion.div>
        </div>
        
        {/* Project Details */}
        <div className="w-full p-6 flex flex-col overflow-auto project-modal-content">
          <motion.h2
            id="modal-title"
            className={`text-2xl font-bold mb-2 ${getFontClass()}`}
            style={{ color: project.color }}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.2 }}
          >
            {project.id === 1 ? t('projects.projectTitles.watches') :
             project.id === 2 ? t('projects.projectTitles.vusto') :
             project.id === 3 ? t('projects.projectTitles.reelify') :
             project.id === 4 ? t('projects.projectTitles.elegance') :
             project.id === 5 ? t('projects.projectTitles.flux') :
             project.id === 6 ? t('projects.projectTitles.pubg') :
             project.id === 7 ? t('projects.projectTitles.teslaShop') :
             project.title}
          </motion.h2>
          
          <motion.div 
            className="flex flex-wrap gap-2 mb-4"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.5, delay: 0.3 }}
          >
            {project.tags.map((tag, index) => (
              <motion.div
                key={tag}
                initial={{ opacity: 0, x: -10 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.3, delay: 0.4 + (index * 0.1) }}
              >
                <Badge 
                  className="flex items-center gap-1 transition-all duration-300 hover:shadow-md hover:shadow-[rgba(0,0,0,0.2)] hover:-translate-y-0.5"
                  style={{ backgroundColor: `${project.color}20`, color: project.color }}
                >
                  <Tag className="h-3 w-3" />
                  {tag}
                </Badge>
              </motion.div>
            ))}
          </motion.div>
          
          <div className="flex-1 mb-6">
            {/* Tabs Navigation */}
            <div className="flex border-b border-gray-700 mb-6">
              <button
                className={`px-4 py-2 font-medium transition-all duration-300 ${activeTab === 'about' ? 'text-white border-b-2' : 'text-gray-400 hover:text-gray-200'}`}
                style={{ borderColor: activeTab === 'about' ? project.color : 'transparent' }}
                onClick={() => setActiveTab('about')}
              >
                {t('projects.about')}
              </button>
              {project.id === 1 && (
                <button
                  className={`px-4 py-2 font-medium transition-all duration-300 ${activeTab === 'overview' ? 'text-white border-b-2' : 'text-gray-400 hover:text-gray-200'}`}
                  style={{ borderColor: activeTab === 'overview' ? project.color : 'transparent' }}
                  onClick={() => setActiveTab('overview')}
                >
                  {t('projects.overview')}
                </button>
              )}
              {project.features && (
                <button
                  className={`px-4 py-2 font-medium transition-all duration-300 ${activeTab === 'features' ? 'text-white border-b-2' : 'text-gray-400 hover:text-gray-200'}`}
                  style={{ borderColor: activeTab === 'features' ? project.color : 'transparent' }}
                  onClick={() => setActiveTab('features')}
                >
                  {t('projects.features')}
                </button>
              )}
              {project.technologies && (
                <button
                  className={`px-4 py-2 font-medium transition-all duration-300 ${activeTab === 'tech' ? 'text-white border-b-2' : 'text-gray-400 hover:text-gray-200'}`}
                  style={{ borderColor: activeTab === 'tech' ? project.color : 'transparent' }}
                  onClick={() => setActiveTab('tech')}
                >
                  {t('projects.technologies')}
                </button>
              )}
            </div>
            
            {/* Tab Content */}
            <AnimatePresence mode="wait">
              {activeTab === 'overview' && project.id === 1 && (
                <motion.div
                  key="overview"
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -10 }}
                  transition={{ duration: 0.3 }}
                >
                  <p className="text-gray-300 leading-relaxed">
                    {t('projects.watchesOverview')}
                  </p>
                </motion.div>
              )}
              
              {activeTab === 'about' && (
                <motion.div
                  key="about"
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -10 }}
                  transition={{ duration: 0.3 }}
                >
                  <p className="text-gray-300 leading-relaxed">
                    {project.id === 1 ? t('projects.watchesDescription') :
                     project.id === 2 ? t('projects.vustoDescription') :
                     project.id === 3 ? t('projects.reelifyDescription') :
                     project.id === 4 ? t('projects.eleganceDescription') :
                     project.id === 5 ? t('projects.fluxDescription') :
                     project.id === 6 ? t('projects.pubgDescription') :
                     project.description}
                  </p>
                </motion.div>
              )}
              
              {activeTab === 'features' && project.features && (
                <motion.div
                  key="features"
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -10 }}
                  transition={{ duration: 0.3 }}
                  className="space-y-3"
                >
                  {project.id === 1 ? (
                    <>
                      {[
                        t('projects.watchesFeature1'),
                        t('projects.watchesFeature2'),
                        t('projects.watchesFeature3'),
                        t('projects.watchesFeature4'),
                        t('projects.watchesFeature5'),
                        t('projects.watchesFeature6'),
                        t('projects.watchesFeature7')
                      ].map((feature, index) => (
                        <motion.div 
                          key={index}
                          className="flex items-start gap-3 p-3 rounded-md transition-all duration-300 hover:bg-white/5"
                          initial={{ opacity: 0, x: -20 }}
                          animate={{ opacity: 1, x: 0 }}
                          transition={{ duration: 0.3, delay: index * 0.1 }}
                        >
                          <div 
                            className="p-2 rounded-full flex-shrink-0"
                            style={{ backgroundColor: `${project.color}20`, color: project.color }}
                          >
                            {getFeatureIcon(index)}
                          </div>
                          <div>
                            <p className="text-gray-200">{feature}</p>
                          </div>
                        </motion.div>
                      ))}
                    </>
                  ) : project.id === 2 ? (
                    <>
                      {[
                        t('projects.vustoFeature1'),
                        t('projects.vustoFeature2'),
                        t('projects.vustoFeature3'),
                        t('projects.vustoFeature4'),
                        t('projects.vustoFeature5'),
                        t('projects.vustoFeature6')
                      ].map((feature, index) => (
                        <motion.div 
                          key={index}
                          className="flex items-start gap-3 p-3 rounded-md transition-all duration-300 hover:bg-white/5"
                          initial={{ opacity: 0, x: -20 }}
                          animate={{ opacity: 1, x: 0 }}
                          transition={{ duration: 0.3, delay: index * 0.1 }}
                        >
                          <div 
                            className="p-2 rounded-full flex-shrink-0"
                            style={{ backgroundColor: `${project.color}20`, color: project.color }}
                          >
                            {getFeatureIcon(index)}
                          </div>
                          <div>
                            <p className="text-gray-200">{feature}</p>
                          </div>
                        </motion.div>
                      ))}
                    </>
                  ) : project.id === 3 ? (
                    <>
                      {[
                        t('projects.reelifyFeature1'),
                        t('projects.reelifyFeature2'),
                        t('projects.reelifyFeature3'),
                        t('projects.reelifyFeature4'),
                        t('projects.reelifyFeature5')
                      ].map((feature, index) => (
                        <motion.div 
                          key={index}
                          className="flex items-start gap-3 p-3 rounded-md transition-all duration-300 hover:bg-white/5"
                          initial={{ opacity: 0, x: -20 }}
                          animate={{ opacity: 1, x: 0 }}
                          transition={{ duration: 0.3, delay: index * 0.1 }}
                        >
                          <div 
                            className="p-2 rounded-full flex-shrink-0"
                            style={{ backgroundColor: `${project.color}20`, color: project.color }}
                          >
                            {getFeatureIcon(index)}
                          </div>
                          <div>
                            <p className="text-gray-200">{feature}</p>
                          </div>
                        </motion.div>
                      ))}
                    </>
                  ) : project.id === 4 ? (
                    <>
                      {[
                        t('projects.eleganceFeature1'),
                        t('projects.eleganceFeature2'),
                        t('projects.eleganceFeature3'),
                        t('projects.eleganceFeature4'),
                        t('projects.eleganceFeature5'),
                        t('projects.eleganceFeature6'),
                        t('projects.eleganceFeature7')
                      ].map((feature, index) => (
                        <motion.div 
                          key={index}
                          className="flex items-start gap-3 p-3 rounded-md transition-all duration-300 hover:bg-white/5"
                          initial={{ opacity: 0, x: -20 }}
                          animate={{ opacity: 1, x: 0 }}
                          transition={{ duration: 0.3, delay: index * 0.1 }}
                        >
                          <div 
                            className="p-2 rounded-full flex-shrink-0"
                            style={{ backgroundColor: `${project.color}20`, color: project.color }}
                          >
                            {getFeatureIcon(index)}
                          </div>
                          <div>
                            <p className="text-gray-200">{feature}</p>
                          </div>
                        </motion.div>
                      ))}
                    </>
                  ) : project.id === 5 ? (
                    <>
                      {[
                        t('projects.fluxFeature1'),
                        t('projects.fluxFeature2'),
                        t('projects.fluxFeature3'),
                        t('projects.fluxFeature4'),
                        t('projects.fluxFeature5'),
                        t('projects.fluxFeature6')
                      ].map((feature, index) => (
                        <motion.div 
                          key={index}
                          className="flex items-start gap-3 p-3 rounded-md transition-all duration-300 hover:bg-white/5"
                          initial={{ opacity: 0, x: -20 }}
                          animate={{ opacity: 1, x: 0 }}
                          transition={{ duration: 0.3, delay: index * 0.1 }}
                        >
                          <div 
                            className="p-2 rounded-full flex-shrink-0"
                            style={{ backgroundColor: `${project.color}20`, color: project.color }}
                          >
                            {getFeatureIcon(index)}
                          </div>
                          <div>
                            <p className="text-gray-200">{feature}</p>
                          </div>
                        </motion.div>
                      ))}
                    </>
                  ) : project.id === 6 ? (
                    <>
                      {[
                        t('projects.pubgFeature1'),
                        t('projects.pubgFeature2'),
                        t('projects.pubgFeature3'),
                        t('projects.pubgFeature4'),
                        t('projects.pubgFeature5'),
                        t('projects.pubgFeature6'),
                        t('projects.pubgFeature7'),
                        t('projects.pubgFeature8')
                      ].map((feature, index) => (
                        <motion.div 
                          key={index}
                          className="flex items-start gap-3 p-3 rounded-md transition-all duration-300 hover:bg-white/5"
                          initial={{ opacity: 0, x: -20 }}
                          animate={{ opacity: 1, x: 0 }}
                          transition={{ duration: 0.3, delay: index * 0.1 }}
                        >
                          <div 
                            className="p-2 rounded-full flex-shrink-0"
                            style={{ backgroundColor: `${project.color}20`, color: project.color }}
                          >
                            {getFeatureIcon(index)}
                          </div>
                          <div>
                            <p className="text-gray-200">{feature}</p>
                          </div>
                        </motion.div>
                      ))}
                    </>
                  ) : (
                    project.features.map((feature, index) => (
                      <motion.div 
                        key={index}
                        className="flex items-start gap-3 p-3 rounded-md transition-all duration-300 hover:bg-white/5"
                        initial={{ opacity: 0, x: -20 }}
                        animate={{ opacity: 1, x: 0 }}
                        transition={{ duration: 0.3, delay: index * 0.1 }}
                      >
                        <div 
                          className="p-2 rounded-full flex-shrink-0"
                          style={{ backgroundColor: `${project.color}20`, color: project.color }}
                        >
                          {getFeatureIcon(index)}
                        </div>
                        <div>
                          <p className="text-gray-200">{feature}</p>
                        </div>
                      </motion.div>
                    ))
                  )}
                </motion.div>
              )}
              
              {activeTab === 'tech' && project.technologies && (
                <motion.div
                  key="tech"
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -10 }}
                  transition={{ duration: 0.3 }}
                >
                  <p className="text-gray-300 leading-relaxed">
                    {project.id === 1 ? t('projects.watchesTech') :
                     project.id === 2 ? t('projects.vustoTech') :
                     project.id === 3 ? t('projects.reelifyTech') :
                     project.id === 4 ? t('projects.eleganceTech') :
                     project.id === 5 ? t('projects.fluxTech') :
                     project.id === 6 ? t('projects.pubgTech') :
                     project.technologies}
                  </p>
                </motion.div>
              )}
            </AnimatePresence>
          </div>
          
          <motion.div 
            className="flex flex-col sm:flex-row gap-3"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.6 }}
          >
            <InteractiveHoverButton
              variant="transparent"
              className="flex-1"
              style={{ 
                color: project.color,
                '--tw-primary-color': project.color,
                '--tw-primary-foreground': '#121212'
              } as React.CSSProperties}
              text={t('projects.preview')}
              icon={<Eye className="h-4 w-4" />}
              onClick={onPreview}
            />

            <InteractiveHoverButton
              variant="transparent"
              className="flex-1"
              style={{ 
                color: project.color,
                '--tw-primary-color': project.color,
                '--tw-primary-foreground': '#fff'
              } as React.CSSProperties}
              text={t('projects.liveDemo')}
              icon={<Globe className="h-4 w-4" />}
              onClick={() => window.open(project.link, '_blank', 'noopener,noreferrer')}
            />

            {project.github && project.github !== "#" && (
              <InteractiveHoverButton
                variant="transparent"
                className="flex-1"
                style={{ 
                  color: project.color,
                  '--tw-primary-color': project.color,
                  '--tw-primary-foreground': '#fff'
                } as React.CSSProperties}
                text={t('projects.source')}
                icon={<Code className="h-4 w-4" />}
                onClick={() => window.open(project.github, '_blank', 'noopener,noreferrer')}
              />
            )}
          </motion.div>
        </div>
      </motion.div>
    </motion.div>
  );
}
