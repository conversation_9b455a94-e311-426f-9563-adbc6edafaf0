import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { X, ExternalLink, Smartphone, Monitor } from 'lucide-react';
import { Button } from './ui/button';
import { useTranslation } from 'react-i18next';

// CSS to hide scrollbars but maintain scrolling functionality
const noScrollbarCSS = `
  /* Hide scrollbars in preview but keep scrolling functionality */
  .website-preview-container {
    -ms-overflow-style: none !important;
    scrollbar-width: none !important;
  }
  
  .website-preview-container::-webkit-scrollbar {
    display: none !important;
  }
  
  body {
    overflow-y: auto !important;
  }
`;

interface WebsitePreviewProps {
  url: string;
  title: string;
  isOpen: boolean;
  onClose: () => void;
}

export default function WebsitePreview({ url, title, isOpen, onClose }: WebsitePreviewProps) {
  const { t } = useTranslation();
  const [isLoading, setIsLoading] = useState(true);

  const [isMobileView, setIsMobileView] = useState(false);
  const [iframeKey, setIframeKey] = useState(Date.now());
  const [hasError, setHasError] = useState(false);
  
  // Reset loading state when URL changes
  useEffect(() => {
    setIsLoading(true);
    setHasError(false);
    setIframeKey(Date.now());

    // Set a timeout to detect if iframe fails to load
    const errorTimeout = setTimeout(() => {
      if (isLoading) {
        setHasError(true);
        setIsLoading(false);
      }
    }, 8000); // 8 second timeout

    return () => clearTimeout(errorTimeout);
  }, [url, isLoading]);
  
  if (!isOpen) return null;

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      className="fixed inset-0 z-50 flex items-center justify-center bg-black/80 backdrop-blur-sm p-4 md:p-8"
    >
      {/* Apply scrollbar hiding styles globally within the component */}
      <style>{noScrollbarCSS}</style>
      
      <motion.div
        initial={{ scale: 0.9, opacity: 0 }}
        animate={{ scale: 1, opacity: 1 }}
        transition={{ duration: 0.3 }}
        className={`bg-dark border border-white/10 rounded-lg flex flex-col overflow-hidden relative ${isMobileView ? 'w-auto h-auto' : 'w-full max-w-5xl h-[80vh]'}`}
        style={isMobileView ? { maxHeight: 'calc(100vh - 100px)' } : undefined}
      >
        {/* Header */}
        <div className="flex items-center justify-between p-3 border-b border-white/10 bg-black/30">
          <div className="flex items-center">
            <div className="text-sm font-medium text-gray-300 truncate max-w-[200px] md:max-w-xs">{title}</div>
          </div>
          <div className="flex items-center space-x-2">
            <Button
              variant="ghost"
              size="sm"
              className="text-gray-400 hover:text-white"
              onClick={() => setIsMobileView(!isMobileView)}
              title={isMobileView ? t('projects.desktopView') : t('projects.mobileView')}
            >
              {isMobileView ? 
                <Monitor className="h-4 w-4" /> : 
                <Smartphone className="h-4 w-4" />}
            </Button>

            <Button
              variant="ghost"
              size="sm"
              className="text-gray-400 hover:text-white"
              onClick={onClose}
            >
              <X className="h-4 w-4" />
            </Button>
          </div>
        </div>

        {/* Browser content */}
        <div className={`flex-1 relative overflow-hidden flex items-center justify-center ${isMobileView ? 'bg-transparent' : 'bg-white'}`}>
          {isLoading && !hasError && (
            <div className="absolute inset-0 flex items-center justify-center bg-gray-100 z-10">
              <div className="w-8 h-8 border-4 border-neon/30 border-t-neon rounded-full animate-spin"></div>
            </div>
          )}
          {hasError && (
            <div className="absolute inset-0 flex flex-col items-center justify-center bg-gray-900 z-10 text-white p-8">
              <div className="text-center">
                <ExternalLink className="h-12 w-12 text-neon mx-auto mb-4" />
                <h3 className="text-lg font-semibold mb-2">{t('projects.previewBlocked', 'Preview Blocked')}</h3>
                <p className="text-gray-300 mb-4 max-w-md">
                  {t('projects.previewBlockedMessage', 'This website blocks iframe embedding for security. Click below to open it in a new tab.')}
                </p>
                <Button
                  onClick={() => window.open(url, '_blank', 'noopener,noreferrer')}
                  className="bg-neon text-dark hover:bg-neon/90"
                >
                  <ExternalLink className="h-4 w-4 mr-2" />
                  {t('projects.openInNewTab', 'Open in New Tab')}
                </Button>
              </div>
            </div>
          )}
          {isMobileView ? (
            <div className="rounded-xl overflow-hidden shadow-lg border border-gray-800 bg-black" style={{ width: '375px', height: '667px' }}>
              <div className="w-full h-6 bg-black flex items-center justify-center">
                <div className="w-32 h-1 rounded-full bg-gray-700"></div>
              </div>
              <iframe
                key={iframeKey}
                src={url}
                title={title}
                className="border-0 w-full h-[calc(100%-24px)] website-preview-container"
                onLoad={() => setIsLoading(false)}
                sandbox="allow-scripts allow-same-origin allow-forms allow-popups"
                loading="lazy"
                style={{ overflow: 'auto' }}
              />
            </div>
          ) : (
            <iframe
              key={iframeKey}
              src={url}
              title={title}
              className="border-0 w-full h-full website-preview-container"
              onLoad={() => setIsLoading(false)}
              sandbox="allow-scripts allow-same-origin allow-forms allow-popups"
              loading="lazy"
              style={{ overflow: 'auto' }}
            />
          )}
        </div>

        {/* Footer */}
        <div className="p-3 border-t border-white/10 bg-black/30 flex justify-between items-center">
          <div className="text-xs text-gray-400 truncate max-w-[150px] sm:max-w-xs md:max-w-md">{url}</div>
          <div className="flex items-center space-x-2">
            <Button
              variant="outline"
              size="sm"
              className="text-xs border-neon/50 text-neon hover:bg-neon/10 hidden xs:flex"
              onClick={() => {
                setIframeKey(Date.now());
                setIsLoading(true);
              }}
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3 mr-1" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                <path d="M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8"></path>
                <path d="M21 3v5h-5"></path>
                <path d="M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16"></path>
                <path d="M8 16H3v5"></path>
              </svg>
              {t('projects.refresh')}
            </Button>
            <Button
              variant="outline"
              size="sm"
              className="text-xs border-neon/50 text-neon hover:bg-neon/10"
              onClick={() => window.open(url, '_blank')}
            >
              <ExternalLink className="h-3 w-3 mr-1" />
              {t('projects.visitWebsite')}
            </Button>
          </div>
        </div>
      </motion.div>
    </motion.div>
  );
}
