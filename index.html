<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=5.0, user-scalable=yes, viewport-fit=cover" />
    <title>Altayeb | Portfolio</title>
    <meta name="description" content="Altayeb - Portfolio site showcasing creative works and projects" />
    <meta name="author" content="Altayeb" />

    <!-- Open Graph / Facebook -->
    <meta property="og:title" content="Altayeb | Portfolio" />
    <meta property="og:description" content="Specialized in digital art, creative design, and unique web experiences." />
    <meta property="og:type" content="website" />
    <meta property="og:image" content="/favcon.png" />
    <meta property="og:image:alt" content="Altayeb Portfolio" />
    
    <!-- Twitter Card -->
    <meta name="twitter:card" content="summary_large_image" />
    <meta name="twitter:title" content="Altayeb | Portfolio" />
    <meta name="twitter:description" content="Specialized in digital art, creative design, and unique web experiences." />
    <meta name="twitter:image" content="/favcon.png" />
    
    <meta name="theme-color" content="#111111" />
    
    <!-- Favicon -->
    <link rel="icon" href="/favcon.png" type="image/png">
    <link rel="apple-touch-icon" href="/favcon.png">
    
    <!-- Preload Largest Contentful Paint image for projects page -->
    <link rel="preload" href="/projects/optimized/Vusto.webp" as="image" type="image/webp">
    
    <!-- Preconnect to required origins -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>

    <!-- Preload critical fonts to prevent FOUT -->
    <link rel="preload" href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&family=Fustat:wght@200..800&family=Cairo:wght@300;400;500;600;700&family=Rakkas&family=Limelight&display=swap" as="style" onload="this.onload=null;this.rel='stylesheet'">

    <!-- Fallback for browsers without JS -->
    <noscript>
      <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&family=Fustat:wght@200..800&family=Cairo:wght@300;400;500;600;700&family=Rakkas&family=Limelight&display=swap" rel="stylesheet">
    </noscript>
    
    <!-- Inline critical CSS to avoid render blocking -->
    <style>
      /* Critical styles for initial render */
      html {
        overflow-x: hidden;
        scroll-behavior: smooth;
      }

      body {
        margin: 0;
        padding: 0;
        background-color: #111111;
        color: #ffffff;
        /* Use the same font stack as Tailwind config to prevent FOUT */
        font-family: 'Poppins', system-ui, sans-serif;
        overflow-x: hidden;
        min-height: 100vh;
        min-height: 100dvh;
        width: 100%;
        max-width: 100vw;
        box-sizing: border-box;
      }

      /* Prevent layout shifts */
      #root {
        width: 100%;
        max-width: 100vw;
        overflow-x: hidden;
        box-sizing: border-box;
      }

      /* Hide body until CSS is loaded to prevent FOUC */
      body:not(.loaded) {
        visibility: hidden;
      }

      /* Font loading optimization - prevent invisible text during font swap */
      .font-loading {
        font-display: swap;
      }

      /* Ensure consistent font metrics to prevent layout shift */
      .font-poppins {
        font-family: 'Poppins', system-ui, sans-serif;
        font-feature-settings: 'kern' 1;
        font-optical-sizing: auto;
      }

      .font-arabic {
        font-family: 'Fustat', sans-serif;
        font-feature-settings: 'kern' 1;
        font-optical-sizing: auto;
      }

      .font-cairo {
        font-family: 'Cairo', sans-serif;
        font-feature-settings: 'kern' 1;
      }
    </style>

    <!-- Font loading optimization script -->
    <script>
      // Optimize font loading to prevent FOUT
      (function() {
        // Check if fonts are already loaded
        if (document.fonts && document.fonts.ready) {
          document.fonts.ready.then(function() {
            document.body.classList.add('fonts-loaded');
          });
        }

        // Fallback for browsers without Font Loading API
        var fontTimeout = setTimeout(function() {
          document.body.classList.add('fonts-loaded');
        }, 3000); // 3 second timeout

        // Clear timeout if fonts load faster
        if (document.fonts && document.fonts.ready) {
          document.fonts.ready.then(function() {
            clearTimeout(fontTimeout);
          });
        }
      })();
    </script>
  </head>

  <body>
    <div id="root"></div>
    
    <!-- Make body visible once loaded -->
    <script>
      document.body.classList.add('loaded');
    </script>
    
    <!-- Defer non-critical scripts -->
    <script src="https://cdn.gpteng.co/gptengineer.js" defer></script>
    <script type="module" src="/src/main.tsx"></script>
  </body>
</html>
