/**
 * Advanced image optimization script for Tesla Shop
 * Requires: npm install sharp
 */

const sharp = require('sharp');
const fs = require('fs');
const path = require('path');

const inputPath = path.join(__dirname, '../public/projects/Tesla-Shop.png');
const outputDir = path.join(__dirname, '../public/projects/optimized');

// Ensure output directory exists
if (!fs.existsSync(outputDir)) {
  fs.mkdirSync(outputDir, { recursive: true });
}

async function optimizeImages() {
  try {
    console.log('🚀 Starting Tesla Shop image optimization...');
    
    // Check if input file exists
    if (!fs.existsSync(inputPath)) {
      console.log('❌ Tesla-Shop.png not found in public/projects/');
      return;
    }

    const image = sharp(inputPath);
    const metadata = await image.metadata();
    console.log(`📏 Original image: ${metadata.width}x${metadata.height}`);

    // Generate different sizes and formats
    const tasks = [
      // WebP versions
      {
        output: path.join(outputDir, 'Tesla-Shop.webp'),
        format: 'webp',
        quality: 85,
        description: 'Main WebP version'
      },
      {
        output: path.join(outputDir, 'Tesla-Shop-400.webp'),
        format: 'webp',
        width: 400,
        quality: 85,
        description: 'Small WebP (400px)'
      },
      {
        output: path.join(outputDir, 'Tesla-Shop-800.webp'),
        format: 'webp',
        width: 800,
        quality: 85,
        description: 'Medium WebP (800px)'
      },
      {
        output: path.join(outputDir, 'Tesla-Shop-1200.webp'),
        format: 'webp',
        width: 1200,
        quality: 85,
        description: 'Large WebP (1200px)'
      },
      // AVIF version (best compression)
      {
        output: path.join(outputDir, 'Tesla-Shop.avif'),
        format: 'avif',
        quality: 75,
        description: 'AVIF version (best compression)'
      }
    ];

    // Process each task
    for (const task of tasks) {
      let processor = sharp(inputPath);
      
      if (task.width) {
        processor = processor.resize(task.width, null, {
          withoutEnlargement: true,
          fit: 'inside'
        });
      }

      if (task.format === 'webp') {
        processor = processor.webp({ quality: task.quality });
      } else if (task.format === 'avif') {
        processor = processor.avif({ quality: task.quality });
      }

      await processor.toFile(task.output);
      
      const stats = fs.statSync(task.output);
      const sizeKB = Math.round(stats.size / 1024);
      console.log(`✅ ${task.description}: ${sizeKB}KB`);
    }

    console.log('🎉 Tesla Shop image optimization complete!');
    console.log('📁 Optimized images saved to public/projects/optimized/');
    
  } catch (error) {
    console.error('❌ Error optimizing images:', error.message);
    console.log('💡 Make sure you have sharp installed: npm install sharp');
  }
}

// Run if called directly
if (require.main === module) {
  optimizeImages();
}

module.exports = optimizeImages;
