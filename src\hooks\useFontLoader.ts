import { useEffect, useState } from 'react';
import { fontLoader, FontConfig } from '../utils/fontLoader';

/**
 * React hook for optimized font loading
 */
export function useFontLoader(fonts: FontConfig[] = []) {
  const [fontsLoaded, setFontsLoaded] = useState(false);
  const [fontLoadingError, setFontLoadingError] = useState<string | null>(null);

  useEffect(() => {
    let isMounted = true;

    const loadFonts = async () => {
      try {
        // Load all specified fonts
        await Promise.all(fonts.map(font => fontLoader.preloadFont(font)));
        
        // Wait for all fonts to be ready
        await fontLoader.waitForFonts();
        
        if (isMounted) {
          setFontsLoaded(true);
          setFontLoadingError(null);
        }
      } catch (error) {
        if (isMounted) {
          setFontLoadingError(error instanceof Error ? error.message : 'Font loading failed');
          // Still set fonts as loaded to prevent indefinite loading state
          setFontsLoaded(true);
        }
      }
    };

    loadFonts();

    return () => {
      isMounted = false;
    };
  }, [fonts]);

  return { fontsLoaded, fontLoadingError };
}

/**
 * Hook for getting language-appropriate font classes
 */
export function useFontClass(language: string = 'en', baseClass: string = '') {
  return fontLoader.getFontClass(language, baseClass);
}

/**
 * Hook for checking if specific fonts are loaded
 */
export function useFontStatus(fontFamilies: string[]) {
  const [fontStatuses, setFontStatuses] = useState<Record<string, boolean>>({});

  useEffect(() => {
    const checkFonts = () => {
      const statuses: Record<string, boolean> = {};
      fontFamilies.forEach(family => {
        statuses[family] = fontLoader.isFontLoaded(family);
      });
      setFontStatuses(statuses);
    };

    // Initial check
    checkFonts();

    // Check periodically until all fonts are loaded
    const interval = setInterval(() => {
      checkFonts();
      
      // Stop checking if all fonts are loaded
      if (Object.values(fontStatuses).every(Boolean)) {
        clearInterval(interval);
      }
    }, 100);

    // Cleanup
    return () => clearInterval(interval);
  }, [fontFamilies, fontStatuses]);

  return fontStatuses;
}
