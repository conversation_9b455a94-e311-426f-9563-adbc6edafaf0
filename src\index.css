/* All fonts are now loaded optimally in index.html to prevent FOUT */

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 7%;
    --foreground: 0 0% 100%;

    --card: 0 0% 9%;
    --card-foreground: 0 0% 100%;

    --popover: 0 0% 7%;
    --popover-foreground: 0 0% 100%;

    --primary: 180 100% 50%;
    --primary-foreground: 0 0% 7%;

    --secondary: 0 0% 16%;
    --secondary-foreground: 0 0% 100%;

    --muted: 0 0% 16%;
    --muted-foreground: 0 0% 70%;

    --accent: 180 100% 50%;
    --accent-foreground: 0 0% 7%;

    --destructive: 0 100% 50%;
    --destructive-foreground: 0 0% 100%;

    --border: 0 0% 18%;
    --input: 0 0% 18%;
    --ring: 180 100% 50%;

    --radius: 0.5rem;

    /* Custom viewport height for mobile */
    --vh: 1vh;
  }

  * {
    @apply border-border selection:bg-neon selection:text-dark;
  }
  
  html {
    @apply scroll-smooth;
  }

  body {
    @apply bg-dark text-foreground font-sans antialiased;
    /* Prevent horizontal scrolling on mobile */
    overflow-x: hidden;
    /* Ensure consistent viewport behavior */
    min-height: 100vh;
    min-height: 100dvh; /* Dynamic viewport height for mobile */
  }

  /* Font loading optimization styles */
  .font-loading {
    font-display: swap;
    /* Prevent invisible text during font load */
    visibility: visible !important;
  }

  .fonts-loaded {
    /* Smooth transition when fonts are loaded */
    transition: font-family 0.1s ease-in-out;
  }

  /* Prevent layout shift during font loading */
  .font-sans {
    font-family: 'Poppins', system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  }

  .font-arabic {
    font-family: 'Fustat', system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  }

  .font-cairo {
    font-family: 'Cairo', system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  }

  /* Text truncation utilities */
  .line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  .line-clamp-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  /* Custom utilities */
  .text-gradient {
    @apply bg-gradient-to-r from-neon to-blue-400 bg-clip-text text-transparent;
  }

  .card-hover {
    @apply transition-all duration-300 hover:shadow-[0_0_15px_rgba(0,255,247,0.3)] hover:-translate-y-1;
  }

  .btn-glow {
    @apply hover:animate-glow;
  }

  /* Font utility classes */
  .font-rakkas {
    font-family: 'Rakkas', cursive;
  }

  .font-limelight {
    font-family: 'Limelight', cursive;
  }

  /* Contact page header override */
  .header-override header {
    background-color: transparent !important;
    box-shadow: none !important;
    backdrop-filter: none !important;
  }

  /* RTL Support */
  .rtl {
    direction: rtl;
    font-family: 'Fustat', sans-serif;
    font-optical-sizing: auto;
    font-style: normal;
  }

  /* Fix for navbar spacing in RTL mode */
  .rtl .navbar-items {
    display: flex;
    flex-direction: row-reverse;
    gap: 2rem;
  }
  
  .rtl .navbar-items > * {
    margin-left: 0;
    margin-right: 0;
    padding: 0 0.75rem;
  }
  
  /* Enhanced RTL navbar styles */
  .rtl-navbar {
    display: flex;
    gap: 2rem;
  }
  
  .rtl-navbar > * {
    margin: 0 0.5rem;
  }
  
  /* Add spacing between Arabic text in navbar */
  .rtl .navbar-link {
    letter-spacing: 0.05em;
    padding: 0.5rem 0.75rem;
  }

  /* Ensure RTL consistency in mobile menu */
  .rtl nav.text-right a {
    letter-spacing: 0.03em;
  }

  /* Terminal and code styling */
  .terminal-window {
    box-shadow: 0 10px 30px -10px rgba(0, 0, 0, 0.5), 0 0 30px rgba(0, 255, 247, 0.15);
  }
  
  .terminal-active {
    box-shadow: 0 0 0 2px rgba(0, 255, 247, 0.3), 0 10px 30px -10px rgba(0, 0, 0, 0.5), 0 0 30px rgba(0, 255, 247, 0.15);
  }

  .terminal-body {
    height: 140px;
    overflow-y: auto;
    font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
  }
  
  .terminal-body::-webkit-scrollbar {
    width: 4px;
  }
  
  .terminal-body::-webkit-scrollbar-track {
    background: #1a1a1a;
  }
  
  .terminal-body::-webkit-scrollbar-thumb {
    background: #333;
    border-radius: 4px;
  }
  
  /* Code editor styles */
  .code-editor {
    box-shadow: 0 10px 30px -10px rgba(0, 0, 0, 0.5), 0 0 30px rgba(0, 255, 247, 0.15);
    transform-style: preserve-3d;
    backface-visibility: hidden;
  }
  
  .editor-body pre {
    counter-reset: line;
  }

  .line-numbers {
    min-width: 2.5em;
  }
  
  /* Hide scrollbar but keep functionality */
  .hide-scrollbar::-webkit-scrollbar {
    display: none;
  }
  
  .hide-scrollbar {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }
  
  /* Custom scrollbar styling */
  .editor-body::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }
  
  .editor-body::-webkit-scrollbar-track {
    background: #1a1a1a;
    border-radius: 4px;
  }
  
  .editor-body::-webkit-scrollbar-thumb {
    background: #333;
    border-radius: 4px;
  }
  
  .editor-body::-webkit-scrollbar-thumb:hover {
    background: #444;
  }
  
  /* Syntax highlighting (simplified) */
  .language-typescript .keyword {
    color: #ff79c6;
  }
  
  .language-typescript .string {
    color: #f1fa8c;
  }
  
  .language-typescript .function {
    color: #50fa7b;
  }
  
  .language-typescript .comment {
    color: #6272a4;
  }
  
  @keyframes blink {
    0%, 100% { opacity: 1; }
    50% { opacity: 0; }
  }
  
  .animate-blink {
    animation: blink 1s infinite;
  }
  
  @keyframes pulse-slow {
    0%, 100% { opacity: 0.5; }
    50% { opacity: 0.3; }
  }
  
  .animate-pulse-slow {
    animation: pulse-slow 6s infinite ease-in-out;
  }
  
  /* Grid background pattern */
  .bg-grid-pattern {
    background-image: linear-gradient(rgba(255, 255, 255, 0.05) 1px, transparent 1px),
                      linear-gradient(90deg, rgba(255, 255, 255, 0.05) 1px, transparent 1px);
    background-size: 30px 30px;
  }

  .glass {
    @apply bg-white/5 backdrop-blur-lg border border-white/10 rounded-lg;
  }
  
  /* Perspective container styles for 3D effects */
  .perspective-container {
    perspective: 1500px;
    transform-style: preserve-3d;
  }
  
  /* Angled borders styling */
  .angled-border {
    position: absolute;
    border: 1px solid rgba(0, 255, 247, 0.3);
    border-radius: 12px;
    transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
  }
  
  .angled-border-1 {
    transform: rotate(3deg);
    z-index: -1;
  }
  
  .angled-border-2 {
    transform: rotate(1.5deg);
    z-index: -2;
  }
  
  /* Tab styles for terminal/code switcher */
  .window-tab {
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  }
  
  .window-tab:hover {
    transform: translateY(-2px);
  }

  .window-tab.active::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 2px;
    background: rgba(0, 255, 247, 1);
    box-shadow: 0 0 8px rgba(0, 255, 247, 0.8);
  }
  
  .window-tab:hover:not(.active)::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 2px;
    background: rgba(255, 255, 255, 0.3);
  }

  /* Stacked tabs effect */
  .window-tab[data-active="true"] {
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05),
                0 0 0 1px rgba(0, 255, 247, 0.3), 0 0 15px rgba(0, 255, 247, 0.2);
    transform: translateY(0) !important;
  }
  
  .window-tab[data-active="false"] {
    filter: brightness(0.8);
    transform-origin: center;
  }
  
  .window-tab[data-active="false"]:hover {
    filter: brightness(0.95);
  }
}

@layer components {
  .container {
    @apply px-4 md:px-8 mx-auto max-w-7xl;
    /* Ensure consistent container behavior on mobile */
    width: 100%;
    box-sizing: border-box;
  }

  .section {
    @apply py-16 md:py-24;
    /* Prevent layout shifts */
    position: relative;
    overflow-x: hidden;
  }

  /* Mobile-specific fixes */
  @media (max-width: 768px) {
    .container {
      @apply px-3;
      /* Reduce padding on very small screens */
      max-width: 100vw;
      box-sizing: border-box;
    }

    .section {
      @apply py-12;
      /* Reduce vertical padding on mobile */
      width: 100%;
      max-width: 100vw;
      overflow-x: hidden;
    }

    /* Prevent horizontal scroll on mobile */
    body {
      max-width: 100vw;
      overflow-x: hidden;
    }

    /* Fix for mobile viewport height issues */
    #home {
      min-height: 100vh;
      min-height: 100dvh;
      height: auto;
    }
  }

  /* Additional mobile fixes for very small screens */
  @media (max-width: 480px) {
    .container {
      @apply px-2;
    }

    /* Ensure text doesn't overflow */
    h1, h2, h3 {
      word-wrap: break-word;
      overflow-wrap: break-word;
    }
  }

  /* Fix for mobile layout shift on first load */
  @media (max-width: 1024px) {
    /* Ensure all sections have consistent width */
    section {
      width: 100%;
      max-width: 100vw;
      overflow-x: hidden;
      box-sizing: border-box;
    }

    /* Prevent any element from causing horizontal scroll */
    * {
      max-width: 100%;
      box-sizing: border-box;
    }

    /* Specific fix for hero section */
    #home {
      width: 100vw;
      max-width: 100vw;
      overflow-x: hidden;
      position: relative;
    }

    /* Ensure grid doesn't overflow */
    .grid {
      width: 100%;
      max-width: 100%;
    }
  }
  
  /* Custom progress indicator for skills section */
  .progress-indicator-custom :where(.progress-indicator) {
    background: linear-gradient(90deg, rgba(0,255,247,0.7) 0%, rgba(0,255,247,1) 100%);
    box-shadow: 0 0 10px rgba(0,255,247,0.5);
  }
}
