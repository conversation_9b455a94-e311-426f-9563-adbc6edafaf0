import { useState, useMemo } from 'react';
import { motion, AnimatePresence } from "framer-motion";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { InteractiveHoverButton } from "@/components/ui/interactive-hover-button";
import { ExternalLink, ArrowRight, GitBranch, Globe, Brain, ShoppingCart, Workflow, Database, Eye, Maximize2, Info } from "lucide-react";
import { useTranslation } from 'react-i18next';
import { getFontClass } from '../i18n';
import WebsitePreview from '../components/WebsitePreview';
import ProjectDetailsModal from '../components/ProjectDetailsModal';
import Header from "../components/Header";
import Footer from "../components/Footer";
import { OptimizedImage } from "@/components/ui/optimized-image";
import { SEOHead } from "@/components/SEOHead";

const projects = [
  {
    id: 1,
    title: "Watches Store | متجر ساعات فاخرة",
    description: "A modern, luxurious e-commerce platform specializing in high-end watches, built with React, TypeScript, and Shadcn UI components for an elegant shopping experience.",
    tags: ["React", "TypeScript", "Tailwind CSS", "Shadcn UI", "E-commerce"],
    image: "/projects/Watches-SD.png",
    link: "https://watches-sd.netlify.app/",
    github: "#",
    featured: true,
    color: "#00c8ff",
    icon: <ShoppingCart className="h-5 w-5" />,
    features: [
      "Browse premium luxury watches with detailed product information",
      "Filter products by category, price range, and more",
      "Save favorite items to wishlist",
      "Fully functional shopping cart with quantity controls",
      "User profile management",
      "Responsive design for all devices",
      "Modern UI with Shadcn components"
    ],
    technologies: "Built with React 18 and TypeScript using Vite. Features React Router v6, TailwindCSS with Shadcn UI, Context API for state management, TanStack React Query for data fetching, and React Hook Form with Zod validation. UI enhanced with Radix UI primitives, Embla Carousel, Framer Motion animations, and Sonner toasts."
  },
  {
    id: 2,
    title: "Vusto - Fine Dining Restaurant",
    description: "Elegant, modern restaurant website designed to showcase culinary excellence and provide an exceptional user experience with beautiful, responsive design.",
    tags: ["React", "Tailwind CSS", "Framer Motion", "UI/UX Design"],
    image: "/projects/Vusto.png",
    link: "https://vusto.netlify.app/",
    github: "https://github.com/altyb/Vusto",
    featured: true,
    color: "#00e5c8",
    icon: <ShoppingCart className="h-5 w-5" />,
    features: [
      "Stunning visual design with parallax effects",
      "Responsive layout for all devices",
      "Interactive menu with beautiful presentation",
      "Reservation system with contact form",
      "Testimonials and customer reviews",
      "Subtle scroll reveal animations"
    ],
    technologies: "Built with React and TypeScript, using Vite for development. Features shadcn/ui components, Tailwind CSS for styling, Framer Motion for animations, and React Router for navigation."
  },
  {
    id: 3,
    title: "Reelify - Movie Exploration Platform",
    description: "A simple yet powerful platform for exploring the latest movies, watching trailers, and tracking updates on actors and filmmakers with an intuitive interface.",
    tags: ["React", "TypeScript", "Vite", "API Integration", "Movie Database"],
    image: "/projects/Reelify.png",
    link: "https://reelify.netlify.app/",
    github: "#",
    featured: true,
    color: "#00fff7",
    icon: <Brain className="h-5 w-5" />,
    features: [
      "Watch movie trailers streamed directly from a movie database",
      "Search for movies by title, genre, or rating",
      "Stay updated on latest film releases",
      "Explore actor and filmmaker profiles with biographies",
      "Get box office insights on movie performance and earnings"
    ],
    technologies: "Built with TypeScript, React, and Vite for a fast and responsive user experience. Integrated with Movie Database API for comprehensive film information and trailer streaming."
  },
  {
    id: 4,
    title: "Elegance - Modern Arabic Fashion Platform",
    description: "A sophisticated e-commerce platform built for a premium Arabic fashion brand with full RTL support, featuring a luxurious and elegant UI that embodies the brand's identity.",
    tags: ["React", "TypeScript", "Tailwind CSS", "RTL Support", "E-commerce", "Fashion"],
    image: "/projects/RoyalS.png",
    link: "https://royal-s.netlify.app/",
    github: "#",
    featured: true,
    color: "#ff79c6",
    icon: <ShoppingCart className="h-5 w-5" />,
    features: [
      "Complete Arabic localization with RTL support",
      "Extensive product catalog with 48 products across 6 categories",
      "Smart filtering with color swatches and Arabic color names",
      "Wishlist functionality with local storage persistence",
      "Dark/Light mode with refined UI in both themes",
      "Optimized mobile experience with dedicated search and filters",
      "Performance optimizations with useMemo and useCallback"
    ],
    technologies: "Built with React 18, TypeScript, and Vite. Features React Router DOM, TanStack React Query, Tailwind CSS, and Shadcn UI Components (built on Radix UI). Uses Lucide React Icons and Local Storage for cart and wishlist persistence. Optimized with code splitting, search debouncing, and proper skeleton loading states."
  },
  {
    id: 5,
    title: "Flux Digital Canvas - Digital Services Marketplace",
    description: "Sophisticated, responsive e-commerce platform specializing in digital services with multi-language support, dynamic theme customization, and a robust shopping cart system.",
    tags: ["React", "Tailwind CSS", "TypeScript", "Framer Motion"],
    image: "/projects/Digital-Mart.png",
    link: "https://digital-mart.netlify.app/",
    github: "#",
    featured: true,
    color: "#bd93f9",
    icon: <ShoppingCart className="h-5 w-5" />,
    features: [
      "Responsive design optimized for all devices",
      "Multilingual support with RTL capabilities",
      "Theme customization with light/dark modes",
      "Interactive UI with 3D animations",
      "E-commerce functionality with persistent cart",
      "Admin dashboard with analytics"
    ],
    technologies: "Built with React and TypeScript using Vite. Features Tailwind CSS and Shadcn UI for styling, Framer Motion for animations, and Context API for state management. Optimized with code splitting and lazy loading."
  },
  {
    id: 6,
    title: "PUBG RNG Store - Gaming Marketplace",
    description: "Comprehensive marketplace for PUBG Mobile players, providing a secure and feature-rich platform to buy, sell, and discover gaming resources with AI-powered features and optimized performance.",
    tags: ["React", "TypeScript", "Firebase", "AI Integration", "E-commerce", "Gaming"],
    image: "/projects/RNG-Store.png",
    link: "https://rngstore.vip/",
    github: "#",
    featured: true,
    color: "#50fa7b",
    icon: <ShoppingCart className="h-5 w-5" />,
    features: [
      "PUBG Accounts Marketplace with verification system",
      "UC Store with competitive pricing and instant delivery",
      "Mods & Hacks section with compatibility verification",
      "Blog & Community for latest PUBG news and updates",
      "Dark/Light Mode with customizable interface theme",
      "Multi-language support with RTL capabilities",
      "AI-powered content analysis and price recommendations",
      "Comprehensive SEO optimization with structured data"
    ],
    technologies: "Built with React 18, TypeScript, and Vite for the frontend. Uses Tailwind CSS, Shadcn UI, and Framer Motion for styling. Features Firebase (Firestore, Authentication, Storage, Hosting) for backend services. Implements React Router, React Query, and Context API for state management. Integrates with OpenRouter API for AI capabilities and includes comprehensive SEO optimization."
  },
  {
    id: 7,
    title: "Tesla Shop - Gaming Platform",
    description: "Modern Arabic-first gaming cards and digital services platform with comprehensive store, wallet system, and admin dashboard.",
    tags: ["Next.js 15", "TypeScript", "Gaming", "RTL Support"],
    image: "/projects/Tesla-Shop.png",
    link: "https://tesla-shop-nine.vercel.app/",
    github: "#",
    featured: true,
    color: "#8B2635",
    icon: <ShoppingCart className="h-5 w-5" />,
    features: [
      "Gaming Cards Store - Purchase gems, memberships, and battle passes",
      "Digital Wallet System - Secure balance management and transactions",
      "User Management - Profile management with Arabic support",
      "Admin Dashboard - Comprehensive admin panel for platform management",
      "Mobile-First Design - Responsive design optimized for all devices",
      "Dark Theme - Modern dark UI with red accent colors",
      "Real-time Updates - Live order status and balance updates",
      "Copy Utilities - Easy-to-use copy buttons for IDs and codes",
      "Arabic RTL Support - Full right-to-left language support"
    ],
    technologies: "Built with Next.js 15 and TypeScript for the frontend. Uses Radix UI for accessible, unstyled UI components and Tailwind CSS for utility-first styling. Features Context API for state management, security headers for built-in optimizations, and comprehensive Arabic RTL support for the gaming community."
  },
];

export default function Projects() {
  const { t } = useTranslation();
  const [showAll, setShowAll] = useState(false);
  const [previewUrl, setPreviewUrl] = useState<string>("");
  const [previewTitle, setPreviewTitle] = useState<string>("");
  const [isPreviewOpen, setIsPreviewOpen] = useState(false);
  const [selectedProject, setSelectedProject] = useState<Project | null>(null);
  const [isDetailsOpen, setIsDetailsOpen] = useState(false);
  
  const displayedProjects = useMemo(() =>
    showAll
      ? projects
      : projects.filter(project => project.featured),
    [showAll]
  );
  
  const fadeInUp = {
    hidden: { y: 60, opacity: 0 },
    visible: { y: 0, opacity: 1, transition: { duration: 0.6 } }
  };

  const staggerContainer = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2
      }
    }
  };
  
  const openPreview = (url: string, title: string) => {
    setPreviewUrl(url);
    setPreviewTitle(title);
    setIsPreviewOpen(true);
  };
  
  const closePreview = () => {
    setIsPreviewOpen(false);
  };
  
  const openDetails = (project: Project) => {
    setSelectedProject(project);
    setIsDetailsOpen(true);
  };
  
  const closeDetails = () => {
    setIsDetailsOpen(false);
  };
  
  return (
    <div className="min-h-screen bg-dark">
      <SEOHead
        title={t('projects.title', 'Projects')}
        description={t('projects.subtitle', 'Explore my portfolio of modern web applications, e-commerce platforms, and AI-enhanced projects built with cutting-edge technologies.')}
        url="/projects"
        keywords={['portfolio', 'projects', 'web development', 'e-commerce', 'React', 'TypeScript', 'AI projects']}
      />
      <Header />
      <main className="pt-20">
        
        <section id="projects" className="section py-16">
      <div className="container">
        <motion.div 
          className="text-center max-w-3xl mx-auto mb-16"
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true, margin: "-100px" }}
          variants={fadeInUp}
        >
          <h2 className={`text-3xl md:text-5xl font-bold mb-4 ${getFontClass()}`}>{t('projects.title')}</h2>
          <div className="h-1 w-20 bg-neon mx-auto mb-6 rounded-full"></div>
          <p className="text-gray-300 text-lg">
            {t('projects.subtitle')}
          </p>
        </motion.div>

        <motion.div
          className="grid grid-cols-2 md:grid-cols-2 lg:grid-cols-3 gap-4 md:gap-10 mb-16"
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true, margin: "-100px" }}
          variants={staggerContainer}
        >
          {displayedProjects.map((project) => (
            <ProjectCard 
              key={project.id} 
              project={project} 
              variants={fadeInUp} 
              onPreview={() => openPreview(project.link, project.title)}
              onDetails={() => openDetails(project)}
            />
          ))}
        </motion.div>

        {!showAll && projects.length > displayedProjects.length && (
          <motion.div 
            className="flex justify-center"
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true, margin: "-100px" }}
            variants={fadeInUp}
          >
            <InteractiveHoverButton 
              onClick={() => setShowAll(true)} 
              className="bg-darkgray text-white text-lg px-8 py-6 h-auto w-auto"
              style={{
                '--tw-primary-color': '#00c8ff',
                '--tw-primary-foreground': '#121212'
              } as React.CSSProperties}
              text={t('projects.viewAll')}
            />
          </motion.div>
        )}
        
        <AnimatePresence>
          {isPreviewOpen && (
            <WebsitePreview 
              url={previewUrl}
              title={previewTitle}
              isOpen={isPreviewOpen}
              onClose={closePreview}
            />
          )}
          
          {isDetailsOpen && selectedProject && (
            <ProjectDetailsModal
              project={selectedProject}
              isOpen={isDetailsOpen}
              onClose={closeDetails}
              onPreview={() => {
                closeDetails();
                openPreview(selectedProject.link, selectedProject.title);
              }}
            />
          )}
        </AnimatePresence>
      </div>
    </section>
      </main>
      <Footer />
    </div>
  );
}

export interface Project {
  id: number;
  title: string;
  description: string;
  tags: string[];
  image: string;
  link: string;
  github: string;
  featured: boolean;
  color: string;
  icon: React.ReactNode;
  features?: string[];
  technologies?: string;
}

interface ProjectCardProps {
  project: Project;
  variants: any;
  onPreview: () => void;
  onDetails: () => void;
}

function ProjectCard({ project, variants, onPreview, onDetails }: ProjectCardProps) {
  const { t } = useTranslation();
  const [isHovered, setIsHovered] = useState(false);
  
  return (
    <motion.div variants={variants}>
      <Card 
        className={`bg-dark border border-white/10 overflow-hidden h-full flex flex-col transform transition-all duration-500 hover:-translate-y-3 hover:shadow-[0_0_30px_rgba(${project.color},0.15)]`}
        onMouseEnter={() => setIsHovered(true)}
        onMouseLeave={() => setIsHovered(false)}
      >
        <div className="relative aspect-[16/9] overflow-hidden group cursor-pointer" onClick={onDetails}>
          {/* Main image with optimized formats */}
          <OptimizedImage 
            src={project.image} 
            alt={project.title}
            className="object-cover w-full h-full transition-all duration-700 group-hover:scale-110 group-hover:brightness-110"
            sizes="(max-width: 640px) 100vw, (max-width: 1024px) 50vw, 33vw"
          />
          
          {/* Overlay gradient */}
          <div className="absolute inset-0 bg-gradient-to-t from-dark to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-end justify-center p-4">
            <Button 
              size="sm" 
              className="bg-white/10 backdrop-blur-md hover:bg-white/20 text-white border border-white/20 w-full"
              onClick={(e) => {
                e.stopPropagation();
                onDetails();
              }}
            >
              <Info className="mr-2 h-3.5 w-3.5" />
              {t('projects.moreInfo')}
            </Button>
          </div>
          
          {/* Badge */}
          <div className="absolute top-4 right-4 z-20">
            <Badge 
              className="flex items-center gap-1.5 px-3 py-1.5 backdrop-blur-md" 
              style={{ backgroundColor: `${project.color}80`, color: '#121212' }}
            >
              {project.icon} {t('projects.featured')}
            </Badge>
          </div>
        </div>
        
        <CardHeader>
          <CardTitle
            className={`text-xl ${getFontClass()} transition-colors duration-300 line-clamp-2`}
            style={{ color: isHovered ? project.color : 'white' }}
          >
            {project.id === 1 ? t('projects.projectTitles.watches') :
             project.id === 2 ? t('projects.projectTitles.vusto') :
             project.id === 3 ? t('projects.projectTitles.reelify') :
             project.id === 4 ? t('projects.projectTitles.elegance') :
             project.id === 5 ? t('projects.projectTitles.flux') :
             project.id === 6 ? t('projects.projectTitles.pubg') :
             project.id === 7 ? t('projects.projectTitles.teslaShop') :
             project.title}
          </CardTitle>
        </CardHeader>
        
        <CardContent className="flex-1">
          <p className="text-gray-300 mb-5 line-clamp-3">
            {project.id === 1 ? t('projects.watchesDescription') :
             project.id === 2 ? t('projects.vustoDescription') :
             project.id === 3 ? t('projects.reelifyDescription') :
             project.id === 4 ? t('projects.eleganceDescription') :
             project.id === 5 ? t('projects.fluxDescription') :
             project.id === 6 ? t('projects.pubgDescription') :
             project.id === 7 ? t('projects.teslaShopDescription') :
             project.description}
          </p>
          <div className="flex flex-wrap gap-2">
            {project.tags.map((tag) => (
              <span 
                key={tag} 
                className="px-3 py-1.5 rounded-full text-xs transition-colors duration-300"
                style={{
                  backgroundColor: isHovered ? `${project.color}20` : '#1e1e1e',
                  color: isHovered ? project.color : '#d4d4d4'
                }}
              >
                {tag}
              </span>
            ))}
          </div>
        </CardContent>
        
        <CardFooter className="flex gap-3">
          <InteractiveHoverButton 
            className="flex-1 border-none bg-transparent hover:bg-transparent"
            style={{
              color: project.color,
              '--tw-primary-color': project.color,
              '--tw-primary-foreground': '#fff'
            } as React.CSSProperties}
            text={t('projects.explore')}
            onClick={onDetails}
          />
          <InteractiveHoverButton 
            className="flex-1 border-none bg-transparent hover:bg-transparent"
            style={{
              color: project.color,
              '--tw-primary-color': project.color,
              '--tw-primary-foreground': '#fff'
            } as React.CSSProperties}
            text={t('projects.preview')}
            onClick={onPreview}
          />
        </CardFooter>
      </Card>
    </motion.div>
  );
}
