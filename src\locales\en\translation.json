{"header": {"home": "Home", "about": "About", "projects": "Projects", "blog": "Blog", "contact": "Contact", "language": "عربي"}, "hero": {"title": "Software & Prompt Engineer", "greeting": "Hi 👋! I'm", "name": "Altayeb", "role": "a Full Stack Developer", "description": "Specialized in AI-enhanced applications, prompt engineering, and crafting exceptional e-commerce experiences with multilingual support including RTL languages. Bridging human creativity with AI capabilities to build innovative solutions.", "viewProjects": "View Projects", "contactMe": "Contact Me", "scrollDown": "Scroll Down", "skills": {"promptEngineering": "Prompt Engineering Expert", "aiWebApps": "AI-Enhanced Web Apps", "workflow": "Workflow Automation", "responsive": "Responsive Experiences", "ecommerce": "E-commerce Platforms"}}, "about": {"title": "About Me", "subtitle": "Full Stack Developer specializing in AI-enhanced applications and modern web technologies.", "story": {"title": "My Story", "p1": "I'm a Prompt Engineering specialist leveraging AI to solve complex problems and build efficient solutions, with expertise in modern web technologies.", "p2": "Skilled in developing AI-enhanced applications using Claude AI and other large language models, I'm passionate about combining human creativity with AI capabilities.", "p3": "I create responsive and elegant e-commerce platforms with multilingual support including RTL languages, and I'm an advocate for AI-assisted development to streamline workflows.", "p4": "My focus is on building accessible applications that provide exceptional user experiences, using modern UI frameworks like Tailwind CSS and Shadcn UI components."}, "development": {"title": "Development Focus", "ecommerce": "E-commerce", "ecommerceDesc": "Building sophisticated online shopping experiences", "aiIntegration": "AI Integration", "aiIntegrationDesc": "Integrating large language models into web apps", "webApps": "Web Apps", "webAppsDesc": "Modern, high-performance web applications", "mobile": "Mobile", "mobileDesc": "Fully responsive designs across all devices"}, "skills": {"title": "Skills & Expertise"}, "viewProjects": "View My Projects"}, "skills": {"title": "Technical Proficiency", "subtitle": "My expertise and skill levels in various technologies and development areas"}, "projects": {"title": "Featured Projects", "subtitle": "Explore my portfolio of AI-enhanced applications and integrated solutions, showcasing expertise in prompt engineering, e-commerce, and multilingual development.", "projectTitles": {"watches": "Watches Store | Luxury Timepieces", "vusto": "Vusto - Fine Dining Restaurant", "reelify": "Reelify - Movie Exploration Platform", "elegance": "Elegance - Modern Arabic Fashion Platform", "flux": "Flux Digital Canvas - Digital Services Marketplace", "pubg": "PUBG RNG Store - Gaming Marketplace", "teslaShop": "Tesla Shop - Gaming Platform"}, "viewAll": "View All Projects", "liveDemo": "Live Demo", "source": "Source", "featured": "Featured", "preview": "Preview", "fullPreview": "Full Preview", "openInNewTab": "Open in new tab", "visitWebsite": "Visit Website", "refresh": "Refresh", "mobileView": "Mobile View", "desktopView": "Desktop View", "details": "Details", "viewDetails": "View Details", "moreInfo": "More Info", "explore": "Explore", "about": "About", "overview": "Overview", "features": "Key Features", "technologies": "Technologies Used", "vustoDescription": "Elegant, modern restaurant website designed to showcase culinary excellence and provide an exceptional user experience with beautiful, responsive design.", "vustoFeature1": "Stunning visual design with parallax effects", "vustoFeature2": "Responsive layout for all devices", "vustoFeature3": "Interactive menu with beautiful presentation", "vustoFeature4": "Reservation system with contact form", "vustoFeature5": "Testimonials and customer reviews", "vustoFeature6": "Subtle scroll reveal animations", "vustoTech": "Built with React and TypeScript, using Vite for development. Features shadcn/ui components, Tailwind CSS for styling, Framer Motion for animations, and React Router for navigation.", "fluxDescription": "Sophisticated, responsive e-commerce platform specializing in digital services with multi-language support, dynamic theme customization, and a robust shopping cart system.", "fluxFeature1": "Responsive design optimized for all devices", "fluxFeature2": "Multilingual support with RTL capabilities", "fluxFeature3": "Theme customization with light/dark modes", "fluxFeature4": "Interactive UI with 3D animations", "fluxFeature5": "E-commerce functionality with persistent cart", "fluxFeature6": "Admin dashboard with analytics", "fluxTech": "Built with React and TypeScript using Vite. Features Tailwind CSS and Shadcn UI for styling, Framer Motion for animations, and Context API for state management. Optimized with code splitting and lazy loading.", "pubgDescription": "Comprehensive marketplace for PUBG Mobile players, providing a secure and feature-rich platform to buy, sell, and discover gaming resources with AI-powered features and optimized performance.", "pubgFeature1": "PUBG Accounts Marketplace with verification system", "pubgFeature2": "UC Store with competitive pricing and instant delivery", "pubgFeature3": "Mods & Hacks section with compatibility verification", "pubgFeature4": "Blog & Community for latest PUBG news and updates", "pubgFeature5": "Dark/Light Mode with customizable interface theme", "pubgFeature6": "Multi-language support with RTL capabilities", "pubgFeature7": "AI-powered content analysis and price recommendations", "pubgFeature8": "Comprehensive SEO optimization with structured data", "pubgTech": "Built with React 18, TypeScript, and Vite for the frontend. Uses Tailwind CSS, Shadcn UI, and Framer Motion for styling. Features Firebase (Firestore, Authentication, Storage, Hosting) for backend services. Implements React Router, React Query, and Context API for state management. Integrates with OpenRouter API for AI capabilities and includes comprehensive SEO optimization.", "teslaShopDescription": "Modern Arabic-first gaming platform with comprehensive store, wallet system, and admin dashboard.", "teslaShopFeature1": "Gaming Cards Store - Purchase gems, memberships, and battle passes", "teslaShopFeature2": "Digital Wallet System - Secure balance management and transactions", "teslaShopFeature3": "User Management - Profile management with Arabic support", "teslaShopFeature4": "Admin Dashboard - Comprehensive admin panel for platform management", "teslaShopFeature5": "Mobile-First Design - Responsive design optimized for all devices", "teslaShopFeature6": "Dark Theme - Modern dark UI with red accent colors", "teslaShopFeature7": "Real-time Updates - Live order status and balance updates", "teslaShopFeature8": "Copy Utilities - Easy-to-use copy buttons for IDs and codes", "teslaShopFeature9": "Arabic RTL Support - Full right-to-left language support", "teslaShopTech": "Built with Next.js 15 and TypeScript for the frontend. Uses Radix UI for accessible, unstyled UI components and Tailwind CSS for utility-first styling. Features Context API for state management, security headers for built-in optimizations, and comprehensive Arabic RTL support for the gaming community.", "watchesDescription": "A modern, luxurious e-commerce platform specializing in high-end watches, built with React, TypeScript, and Shadcn UI components for an elegant shopping experience.", "watchesOverview": "The Watches Store is a modern, luxurious e-commerce platform specializing in high-end watches. Built with React, TypeScript, and Shadcn UI components, it delivers an elegant shopping experience with comprehensive features including product filtering, wishlist functionality, a fully functional shopping cart with quantity controls, user profile management, and responsive design optimized for all devices. The platform leverages React 18, React Router v6, TailwindCSS, Context API for state management, TanStack React Query for data fetching, and React Hook Form with Zod validation. The UI is enhanced with Radix UI primitives, Embla Carousel, Framer Motion animations, and Sonner toasts to create a premium shopping experience for luxury watch enthusiasts.", "watchesFeature1": "Browse premium luxury watches with detailed product information", "watchesFeature2": "Filter products by category, price range, and more", "watchesFeature3": "Save favorite items to wishlist", "watchesFeature4": "Fully functional shopping cart with quantity controls", "watchesFeature5": "User profile management", "watchesFeature6": "Responsive design for all devices", "watchesFeature7": "Modern UI with Shadcn components", "watchesTech": "Built with React 18 and TypeScript using Vite. Features React Router v6, TailwindCSS with Shadcn UI, Context API for state management, TanStack React Query for data fetching, and React Hook Form with Zod validation. UI enhanced with Radix UI primitives, Embla Carousel, Framer Motion animations, and Sonner toasts.", "eleganceDescription": "A sophisticated e-commerce platform built for a premium Arabic fashion brand with full RTL support, featuring a luxurious and elegant UI that embodies the brand's identity.", "eleganceFeature1": "Complete Arabic localization with RTL support", "eleganceFeature2": "Extensive product catalog with 48 products across 6 categories", "eleganceFeature3": "Smart filtering with color swatches and Arabic color names", "eleganceFeature4": "Wishlist functionality with local storage persistence", "eleganceFeature5": "Dark/Light mode with refined UI in both themes", "eleganceFeature6": "Optimized mobile experience with dedicated search and filters", "eleganceFeature7": "Performance optimizations with useMemo and useCallback", "eleganceTech": "Built with React 18, TypeScript, and Vite. Features React Router DOM, TanStack React Query, Tailwind CSS, and Shadcn UI Components (built on Radix UI). Uses Lucide React Icons and Local Storage for cart and wishlist persistence. Optimized with code splitting, search debouncing, and proper skeleton loading states.", "reelifyDescription": "A simple yet powerful platform for exploring the latest movies, watching trailers, and tracking updates on actors and filmmakers with an intuitive interface.", "reelifyFeature1": "Watch movie trailers streamed directly from a movie database", "reelifyFeature2": "Search for movies by title, genre, or rating", "reelifyFeature3": "Stay updated on latest film releases", "reelifyFeature4": "Explore actor and filmmaker profiles with biographies", "reelifyFeature5": "Get box office insights on movie performance and earnings", "reelifyTech": "Built with TypeScript, React, and Vite for a fast and responsive user experience. Integrated with Movie Database API for comprehensive film information and trailer streaming."}, "contact": {"title": "Get In Touch", "subtitle": "Have a project in mind or want to discuss collaboration opportunities? I'd love to hear from you!", "letsCollaborate": "Let's Collaborate", "collaborationText": "I'm a passionate developer specializing in web development and AI integration. Whether you need a stunning website, a complex web application, or AI-powered solutions, I'm here to bring your vision to life.", "myDetails": "My Contact Details", "emailLabel": "Email", "phoneLabel": "Phone", "whatsappLabel": "WhatsApp", "socialProfiles": "Social Profiles", "messageSent": "Message sent!", "thankYouMessage": "Thanks {{name}}! I'll get back to you at {{email}} soon.", "form": {"name": "Your Name", "email": "Your Email", "subject": "Subject", "message": "Your Message", "send": "Send Message", "sending": "Sending..."}}, "footer": {"building": "Building intelligent web experiences", "rights": "All rights reserved.", "quickLinks": "Quick Links", "newsletter": {"title": "Subscribe to Newsletter", "description": "Stay updated with my latest projects and articles.", "placeholder": "Your email address", "button": "Subscribe", "thanks": "Thanks for subscribing!", "confirmation": "You'll now receive updates on my latest projects and articles."}}}