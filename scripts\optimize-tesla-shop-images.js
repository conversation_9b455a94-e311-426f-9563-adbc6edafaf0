/**
 * <PERSON><PERSON><PERSON> to create optimized versions of Tesla Shop project image
 * Run this after placing Tesla-Shop.png in public/projects/
 */

const fs = require('fs');
const path = require('path');

// Check if the original image exists
const originalImagePath = path.join(__dirname, '../public/projects/Tesla-Shop.png');
const optimizedDir = path.join(__dirname, '../public/projects/optimized');

if (!fs.existsSync(originalImagePath)) {
  console.log('❌ Tesla-Shop.png not found in public/projects/');
  console.log('Please add the Tesla Shop screenshot as Tesla-Shop.png first.');
  process.exit(1);
}

// Ensure optimized directory exists
if (!fs.existsSync(optimizedDir)) {
  fs.mkdirSync(optimizedDir, { recursive: true });
}

console.log('✅ Tesla-Shop.png found!');
console.log('📝 To create optimized versions, you can use online tools or image processing libraries:');
console.log('');
console.log('Required optimized versions:');
console.log('- Tesla-Shop.webp (main WebP version)');
console.log('- Tesla-Shop.avif (AVIF version)');
console.log('- Tesla-Shop-400.webp (400px width)');
console.log('- Tesla-Shop-800.webp (800px width)');
console.log('- Tesla-Shop-1200.webp (1200px width)');
console.log('');
console.log('Online tools you can use:');
console.log('- https://squoosh.app/ (Google\'s image optimizer)');
console.log('- https://tinypng.com/ (PNG/JPG compression)');
console.log('- https://convertio.co/ (Format conversion)');
console.log('');
console.log('Or use this command if you have sharp installed:');
console.log('npm install sharp');
console.log('node scripts/generate-optimized-images.js');
