/**
 * Advanced font loading utility to prevent FOUT (Flash of Unstyled Text)
 * and optimize font loading performance
 */

export interface FontConfig {
  family: string;
  weights?: number[];
  display?: 'auto' | 'block' | 'swap' | 'fallback' | 'optional';
}

export class FontLoader {
  private static instance: FontLoader;
  private loadedFonts: Set<string> = new Set();
  private loadingPromises: Map<string, Promise<void>> = new Map();

  private constructor() {}

  static getInstance(): FontLoader {
    if (!FontLoader.instance) {
      FontLoader.instance = new FontLoader();
    }
    return FontLoader.instance;
  }

  /**
   * Preload a font family to prevent FOUT
   */
  async preloadFont(config: FontConfig): Promise<void> {
    const fontKey = `${config.family}-${config.weights?.join(',') || 'default'}`;
    
    if (this.loadedFonts.has(fontKey)) {
      return Promise.resolve();
    }

    if (this.loadingPromises.has(fontKey)) {
      return this.loadingPromises.get(fontKey)!;
    }

    const loadPromise = this.loadFontFamily(config);
    this.loadingPromises.set(fontKey, loadPromise);

    try {
      await loadPromise;
      this.loadedFonts.add(fontKey);
    } catch (error) {
      console.warn(`Failed to load font ${config.family}:`, error);
    } finally {
      this.loadingPromises.delete(fontKey);
    }
  }

  private async loadFontFamily(config: FontConfig): Promise<void> {
    if (!('fonts' in document)) {
      // Fallback for browsers without Font Loading API
      return this.fallbackFontLoad(config);
    }

    const weights = config.weights || [400];
    const loadPromises = weights.map(weight => 
      this.loadSingleFont(config.family, weight)
    );

    await Promise.all(loadPromises);
  }

  private async loadSingleFont(family: string, weight: number): Promise<void> {
    const font = new FontFace(family, `url(https://fonts.gstatic.com/s/${family.toLowerCase()}/v1/${family.toLowerCase()}-${weight}.woff2)`, {
      weight: weight.toString(),
      display: 'swap'
    });

    try {
      await font.load();
      document.fonts.add(font);
    } catch (error) {
      // Font loading failed, but we'll continue with fallback
      console.warn(`Failed to load ${family} weight ${weight}:`, error);
    }
  }

  private async fallbackFontLoad(config: FontConfig): Promise<void> {
    return new Promise((resolve) => {
      // Simple timeout-based fallback
      setTimeout(resolve, 100);
    });
  }

  /**
   * Check if a font family is loaded
   */
  isFontLoaded(family: string): boolean {
    if (!('fonts' in document)) {
      return true; // Assume loaded if no Font Loading API
    }

    return document.fonts.check(`1em ${family}`);
  }

  /**
   * Wait for all fonts to be ready
   */
  async waitForFonts(): Promise<void> {
    if ('fonts' in document && document.fonts.ready) {
      await document.fonts.ready;
    }
  }

  /**
   * Get the appropriate font class based on language
   */
  getFontClass(language: string = 'en', baseClass: string = ''): string {
    const fontMap: Record<string, string> = {
      'ar': 'font-arabic',
      'en': 'font-sans',
      'default': 'font-sans'
    };

    const fontClass = fontMap[language] || fontMap.default;
    return baseClass ? `${baseClass} ${fontClass}` : fontClass;
  }

  /**
   * Apply font loading optimization to an element
   */
  optimizeElement(element: HTMLElement, fontFamily: string): void {
    element.style.fontDisplay = 'swap';
    element.style.fontFamily = fontFamily;
    
    // Add loading class until font is ready
    if (!this.isFontLoaded(fontFamily)) {
      element.classList.add('font-loading');
      
      this.waitForFonts().then(() => {
        element.classList.remove('font-loading');
        element.classList.add('font-loaded');
      });
    }
  }
}

// Export singleton instance
export const fontLoader = FontLoader.getInstance();

// Preload critical fonts on module load
if (typeof window !== 'undefined') {
  // Preload the most critical fonts
  fontLoader.preloadFont({ family: 'Poppins', weights: [300, 400, 500, 600, 700] });
  fontLoader.preloadFont({ family: 'Fustat', weights: [200, 300, 400, 500, 600, 700, 800] });
  fontLoader.preloadFont({ family: 'Cairo', weights: [300, 400, 500, 600, 700] });
}
